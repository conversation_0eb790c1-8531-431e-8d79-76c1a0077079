import baostock as bs
import tushare as ts
import pandas as pd
import os
from datetime import datetime, timedelta
import time

def setup_directories():
    """创建必要的目录"""
    if not os.path.exists('5min_close'):
        os.makedirs('5min_close')
        print("✅ 创建文件夹：5min_close")

def get_stock_list():
    """使用tushare获取股票列表"""
    # 设置tushare token
    ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
    pro = ts.pro_api()
    
    print("📋 正在获取股票列表...")
    
    # 获取所有A股股票基本信息
    stock_basic = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
    
    print(f"✅ 获取到 {len(stock_basic)} 只股票")
    
    # 转换为baostock格式的股票代码
    stock_list = []
    for _, row in stock_basic.iterrows():
        ts_code = row['ts_code']  # 如 000001.SZ
        symbol = row['symbol']    # 如 000001
        name = row['name']        # 股票名称
        
        # 转换为baostock格式
        if ts_code.endswith('.SZ'):
            bs_code = f"sz.{symbol}"
        elif ts_code.endswith('.SH'):
            bs_code = f"sh.{symbol}"
        else:
            continue  # 跳过其他交易所
            
        stock_list.append({
            'ts_code': ts_code,
            'bs_code': bs_code,
            'symbol': symbol,
            'name': name
        })
    
    return stock_list

def get_stock_5min_data(stock_info, start_date="2016-01-01"):
    """获取单只股票的5分钟数据"""
    stock_code = stock_info['bs_code']
    symbol = stock_info['symbol']
    name = stock_info['name']
    
    end_date = datetime.now().strftime('%Y-%m-%d')
    
    print(f"📈 正在获取 {stock_code} ({name}) 的5分钟数据...")
    
    try:
        # 获取5分钟K线数据
        rs = bs.query_history_k_data_plus(
            stock_code,
            "date,time,code,open,high,low,close,volume,amount,adjustflag",
            start_date=start_date,
            end_date=end_date,
            frequency="5",  # 5分钟
            adjustflag="3"  # 不复权
        )
        
        if rs.error_code != '0':
            print(f"  ❌ 查询失败：{rs.error_msg}")
            return False
        
        # 收集数据
        data_list = []
        while rs.next():
            data_list.append(rs.get_row_data())
        
        if not data_list:
            print(f"  ⚠️ 无数据")
            return False
        
        # 转换为DataFrame
        df = pd.DataFrame(data_list, columns=rs.fields)
        
        # 选择需要的列并重新排序
        df = df[['code', 'date', 'time', 'open', 'high', 'low', 'close', 'volume', 'amount']]
        
        # 格式化日期：从 2019-01-02 改为 2019/1/2
        date_series = pd.to_datetime(df['date'])
        df['date'] = date_series.dt.year.astype(str) + '/' + date_series.dt.month.astype(str) + '/' + date_series.dt.day.astype(str)
        
        # 格式化时间：从 20190102093500000 提取时分，如 935
        df['time'] = df['time'].astype(str).str[8:12]  # 提取HHMM部分
        df['time'] = df['time'].str.lstrip('0')  # 去掉前导0
        df['time'] = df['time'].replace('', '0')  # 如果全是0则保留一个0
        
        # 重命名列名为中文
        df.columns = ['股票代码', '交易日期', '时间', '开盘价', '最高价', '最低价', '收盘价', '成交量', '成交额']

        # 转换数值列为合适的格式，去掉多余的小数位
        numeric_columns = ['开盘价', '最高价', '最低价', '收盘价']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').round(2)

        # 成交量和成交额转换为整数（去掉小数点），处理NaN值
        df['成交量'] = pd.to_numeric(df['成交量'], errors='coerce').fillna(0).astype(int)
        df['成交额'] = pd.to_numeric(df['成交额'], errors='coerce').fillna(0).astype(int)
        
        # 保存到CSV文件
        filename = f"5min_close/{stock_code}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        print(f"  ✅ 成功保存 {len(df)} 条数据到 {filename}")
        return True
        
    except Exception as e:
        print(f"  ❌ 处理异常：{str(e)}")
        return False

def main(test_mode=True, test_count=5):
    """主函数"""
    print("🚀 开始获取所有股票5分钟数据")
    print("=" * 60)
    
    # 创建目录
    setup_directories()
    
    # 登录baostock
    lg = bs.login()
    if lg.error_code != '0':
        print(f"❌ baostock登录失败：{lg.error_msg}")
        return
    print("✅ baostock登录成功")
    
    try:
        # 获取股票列表
        stock_list = get_stock_list()
        
        if test_mode:
            print(f"\n🧪 测试模式：只处理前 {test_count} 只股票")
            stock_list = stock_list[:test_count]
        else:
            print(f"\n📊 生产模式：将处理全部 {len(stock_list)} 只股票")
            confirm = input("确认要处理所有股票吗？这可能需要很长时间 (y/n): ")
            if confirm.lower() != 'y':
                print("❌ 用户取消操作")
                return
        
        # 统计信息
        success_count = 0
        fail_count = 0
        start_time = time.time()
        
        # 处理每只股票
        for i, stock_info in enumerate(stock_list, 1):
            print(f"\n[{i}/{len(stock_list)}] ", end="")
            
            if get_stock_5min_data(stock_info):
                success_count += 1
            else:
                fail_count += 1
            
            # 每10只股票显示一次进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(stock_list) - i) * avg_time
                print(f"\n📊 进度：{i}/{len(stock_list)} ({i/len(stock_list)*100:.1f}%)")
                print(f"⏱️ 已用时：{elapsed/60:.1f}分钟，预计剩余：{remaining/60:.1f}分钟")
                print(f"✅ 成功：{success_count}，❌ 失败：{fail_count}")
            
            # 避免请求过于频繁
            time.sleep(0.1)
        
        # 最终统计
        total_time = time.time() - start_time
        print(f"\n🎉 处理完成！")
        print(f"📊 总计：{len(stock_list)} 只股票")
        print(f"✅ 成功：{success_count} 只")
        print(f"❌ 失败：{fail_count} 只")
        print(f"⏱️ 总用时：{total_time/60:.1f} 分钟")
        print(f"📁 文件保存在：5min_close/ 文件夹")
        
    finally:
        # 登出baostock
        bs.logout()
        print("✅ baostock登出成功")

if __name__ == "__main__":
    # 处理所有股票
    main(test_mode=False)
